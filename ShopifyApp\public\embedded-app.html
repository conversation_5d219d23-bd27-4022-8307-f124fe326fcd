<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>3D Viewer App</title>

    <!-- Latest Shopify App Bridge -->
    <script src="https://cdn.shopify.com/shopifycloud/app-bridge.js"></script>

    <!-- Shopify Polaris -->
    <link
      rel="stylesheet"
      href="https://unpkg.com/@shopify/polaris@12.0.0/build/esm/styles.css"
    />

    <style>
      body {
        margin: 0;
        padding: 0;
        background: #f6f6f7;
        min-height: 100vh;
      }
      #app {
        opacity: 0;
        transition: opacity 0.3s ease-in;
      }
      #app.loaded {
        opacity: 1;
      }
      .app-container {
        padding: 20px;
      }
      .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          Helvetica, Arial, sans-serif;
      }
      .error {
        color: #bf0711;
        padding: 16px;
        margin: 16px;
        border: 1px solid #fed3d1;
        border-radius: 3px;
        background: #fff4f4;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="loading">Loading...</div>
    </div>

    <script>
      const APP_CONFIG = {
        apiKey: "d36f16d2a6e8767e33a54d83ba172a41",
        host: new URLSearchParams(window.location.search).get("host"),
        forceRedirect: true,
      };

      function showError(message) {
        const app = document.getElementById("app");
        app.innerHTML = `
                <div class="app-container">
                    <div class="error">
                        <p><strong>Error:</strong> ${message}</p>
                        <p>Please try refreshing the page. If the problem persists, contact support.</p>
                    </div>
                </div>
            `;
        app.classList.add("loaded");
      }

      function initializeApp() {
        try {
          console.log("Initializing app with config:", APP_CONFIG);

          if (!APP_CONFIG.host) {
            throw new Error(
              "Missing host parameter. Please access this app through Shopify admin."
            );
          }

          // Initialize App Bridge
          const app = window["app-bridge"].createApp(APP_CONFIG);
          const actions = window["app-bridge"].actions;

          // Get session token utility
          const getSessionToken = window["app-bridge-utils"].getSessionToken;

          // Initialize the app UI
          const appElement = document.getElementById("app");
          appElement.innerHTML = `
                    <div class="app-container">
                        <div class="Polaris-Page">
                            <div class="Polaris-Page__Header">
                                <h1 class="Polaris-Heading">3D Viewer App</h1>
                            </div>
                            <div class="Polaris-Layout">
                                <div class="Polaris-Layout__Section">
                                    <div class="Polaris-Card">
                                        <div class="Polaris-Card__Section">
                                            <p>Welcome to the 3D Viewer App! This app is completely free to use.</p>
                                            <button class="Polaris-Button Polaris-Button--primary" onclick="loadProducts()">
                                                <span class="Polaris-Button__Content">Load Products</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
          appElement.classList.add("loaded");

          // Load products function
          window.loadProducts = async function () {
            try {
              const token = await getSessionToken(app);
              const response = await fetch("/api/products", {
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              });

              if (!response.ok) throw new Error("Failed to load products");

              const data = await response.json();
              console.log("Products loaded:", data);

              // TODO: Display products in the UI
            } catch (error) {
              console.error("Error loading products:", error);
              showError("Failed to load products: " + error.message);
            }
          };

          // App is free - no billing functionality needed

          console.log("App initialized successfully");
        } catch (error) {
          console.error("Error initializing app:", error);
          showError(error.message);
        }
      }

      // Initialize when DOM is ready
      if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", initializeApp);
      } else {
        initializeApp();
      }
    </script>
  </body>
</html>
