import React, { useState, useEffect } from "react";
import {
  X,
  Upload,
  AlertCircle,
  Image,
  Eye,
  EyeOff,
  HelpCircle,
  ExternalLink,
  Link2,
  Key,
  ArrowRight,
  RefreshCw,
  CheckCircle2,
  Store,
  ShoppingBag,
  ShoppingBasket,
  LayoutGrid,
  ShoppingCart,
  Globe,
  Loader2,
} from "lucide-react";
import { ecommerceService } from "../services/ecommerce";
import type { ProductImportResult } from "../types/ecommerce";
import ShopifyProductSelectionModal from "./modals/ShopifyProductSelectionModal";

interface ShopifyCredentials {
  shopName: string;
  apiKey: string;
  password: string;
}

interface Field {
  id: string;
  label: string;
  type: string;
  required: boolean;
  placeholder?: string;
  help: string;
  helpUrl?: string;
  showIf?: (platform: string) => boolean;
}

interface ProductImportModalProps {
  onClose: () => void;
  selectedPlatform?: string;
  onImportComplete: (
    result: ProductImportResult,
    credentials: ShopifyCredentials
  ) => void;
}

const platforms = [
  // In ProductImportModal.tsx, update the Shopify fields:
  {
    id: "shopify",
    name: "Shopify",
    icon: Store,
    helpUrl: "https://shopify.dev/docs/apps/auth/admin-app-access-tokens",
    fields: [
      {
        id: "shopName",
        label: "Shop Name",
        type: "text",
        required: true,
        placeholder: "your-store-name",
        help: "Enter your Shopify store URL",
        showIf: (platform: string) => platform === "shopify",
      },
      {
        id: "apiKey",
        label: "API Key",
        type: "text",
        required: true,
        placeholder: "Enter your API key",
        help: "You can find this in your store's app settings",
        showIf: (platform: string) => platform === "shopify",
      },
      {
        id: "password",
        label: "API Password",
        type: "password",
        required: true,
        placeholder: "Enter your API password",
        help: "You can find this in your store's app settings",
        showIf: (platform: string) => platform === "shopify",
      },
    ],
  },
  // Rest of the platforms array remains unchanged
  {
    id: "woocommerce",
    name: "WooCommerce",
    icon: ShoppingBag,
    helpUrl: "https://woocommerce.com/document/woocommerce-rest-api/",
    fields: [
      {
        id: "siteUrl",
        label: "WordPress Site URL",
        type: "text",
        required: true,
        placeholder: "https://your-site.com",
        help: "Enter your WordPress site URL including https://",
      },
      {
        id: "consumerKey",
        label: "Consumer Key",
        type: "password",
        required: true,
        help: "Generate REST API keys in WooCommerce > Settings > Advanced > REST API",
        helpUrl:
          "https://woocommerce.com/document/woocommerce-rest-api/authentication/",
      },
      {
        id: "consumerSecret",
        label: "Consumer Secret",
        type: "password",
        required: true,
        help: "Your REST API consumer secret",
      },
    ],
  },
  {
    id: "magento",
    name: "Magento",
    icon: ShoppingBasket,
    helpUrl:
      "https://devdocs.magento.com/guides/v2.4/get-started/authentication/gs-authentication-token.html",
    fields: [
      {
        id: "baseUrl",
        label: "Store Base URL",
        type: "text",
        required: true,
        placeholder: "https://your-store.com",
        help: "Your Magento store URL including https://",
      },
      {
        id: "accessToken",
        label: "Integration Access Token",
        type: "password",
        required: true,
        help: "Generate an integration token in Admin > System > Extensions > Integrations",
      },
    ],
  },
  {
    id: "bigcommerce",
    name: "BigCommerce",
    icon: LayoutGrid,
    helpUrl: "https://developer.bigcommerce.com/docs/start/authentication",
    fields: [
      {
        id: "storeHash",
        label: "Store Hash",
        type: "text",
        required: true,
        placeholder: "Store hash from API credentials",
        help: "Found in your store API credentials",
      },
      {
        id: "accessToken",
        label: "API Token",
        type: "password",
        required: true,
        help: "Create API credentials in Settings > Advanced Settings > API Accounts",
      },
    ],
  },
  {
    id: "prestashop",
    name: "PrestaShop",
    icon: ShoppingCart,
    helpUrl:
      "https://devdocs.prestashop-project.org/8/webservice/tutorials/creating-access/",
    fields: [
      {
        id: "shopUrl",
        label: "Shop URL",
        type: "text",
        required: true,
        placeholder: "https://your-shop.com",
        help: "Your PrestaShop store URL including https://",
      },
      {
        id: "webserviceKey",
        label: "Webservice Key",
        type: "password",
        required: true,
        help: "Generate a webservice key in Advanced Parameters > Webservice",
      },
    ],
  },
  {
    id: "webflow",
    name: "Webflow",
    icon: Globe,
    helpUrl:
      "https://developers.webflow.com/docs/getting-started-with-webflows-api",
    fields: [
      {
        id: "siteId",
        label: "Site ID",
        type: "text",
        required: true,
        placeholder: "Your Webflow site ID",
        help: "Found in your site settings under Integrations",
      },
      {
        id: "accessToken",
        label: "API Access Token",
        type: "password",
        required: true,
        help: "Generate an access token in Project Settings > Integrations",
      },
      {
        id: "collectionId",
        label: "Collection ID",
        type: "text",
        required: true,
        placeholder: "Your products collection ID",
        help: "Found in your collection settings",
      },
    ],
  },
];

const ProductImportModal: React.FC<ProductImportModalProps> = ({
  onClose,
  selectedPlatform = "",
  onImportComplete,
}) => {
  const [credentials, setCredentials] = useState<Record<string, string>>({});
  const [importing, setImporting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [visibleFields, setVisibleFields] = useState<Record<string, boolean>>(
    {}
  );
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});
  const [importStats, setImportStats] = useState<{
    total: number;
    imported: number;
    failed: number;
  } | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<
    "idle" | "checking" | "success" | "error"
  >("idle");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [step, setStep] = useState<
    "credentials" | "verify" | "importing" | "select-products"
  >("credentials");

  const [showShopifyProductSelection, setShowShopifyProductSelection] =
    useState(false);

  const platform = platforms.find((p) => p.id === selectedPlatform);

  const toggleFieldVisibility = (fieldId: string) => {
    setVisibleFields((prev) => ({
      ...prev,
      [fieldId]: !prev[fieldId],
    }));
  };

  const handleInputChange = (fieldId: string, value: string) => {
    if (validationErrors[fieldId]) {
      setValidationErrors((prev) => {
        const next = { ...prev };
        delete next[fieldId];
        return next;
      });
    }

    setCredentials((prev) => ({
      ...prev,
      [fieldId]: value,
    }));
  };

  const validateCredentials = () => {
    const errors: Record<string, string> = {};

    platform?.fields
      .filter(
        (field) =>
          !field.showIf ||
          eval(
            field.showIf.replace("authMethod", `'${credentials.authMethod}'`)
          )
      )
      .forEach((field) => {
        const value = credentials[field.id];

        if (field.required && !value?.trim()) {
          errors[field.id] = `${field.label} is required`;
        }

        if (platform.id === "shopify") {
          if (
            field.id === "apiKey" &&
            value &&
            !/^[a-f0-9]{32}$/i.test(value)
          ) {
            errors[field.id] = "Invalid API Key format";
          }
          if (
            field.id === "apiSecret" &&
            value &&
            !/^shpss_[a-f0-9]{32,64}$/i.test(value)
          ) {
            errors[field.id] =
              'Invalid API Secret format. Should start with "shpss_"';
          }
          if (
            field.id === "accessToken" &&
            value &&
            !/^shp(at|ca)_[a-f0-9]{32,64}$/i.test(value)
          ) {
            errors[field.id] =
              'Invalid access token format. Should start with "shpat_" or "shpca_"';
          }
        }

        if (field.id === "siteUrl" && value) {
          if (!/^https?:\/\//.test(value)) {
            errors[field.id] = "URL must start with http:// or https://";
          }
        }
      });

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleNext = () => {
    if (validateCredentials()) {
      setStep("verify");
    }
  };

  const handleShopifyProductImport = async (selectedProductIds: string[]) => {
    setError(null);
    setIsSubmitting(true);

    try {
      const result = await ecommerceService.importSelectedShopifyProducts(
        {
          shopName: credentials.shopName,
          apiKey: credentials.apiKey,
          password: credentials.password,
        },
        selectedProductIds
      );

      if (result.success) {
        setSuccess(true);
        setTimeout(() => {
          onImportComplete(result, {
            shopName: credentials.shopName,
            apiKey: credentials.apiKey,
            password: credentials.password,
          });
        }, 1000);
      } else {
        setError(result.error || "Import failed");
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unexpected error occurred"
      );
    } finally {
      setIsSubmitting(false);
      setShowShopifyProductSelection(false);
    }
  };

  const handleImport = async () => {
    try {
      if (!validateCredentials()) {
        setConnectionStatus("error");
        return;
      }

      setIsConnecting(true);
      setConnectionStatus("checking");
      setError(null);

      if (!platform) {
        throw new Error("Please select a platform");
      }

      // For Shopify, show the product selection modal instead of importing directly
      if (selectedPlatform === "shopify") {
        setConnectionStatus("idle");
        setIsConnecting(false);
        setShowShopifyProductSelection(true);
        return;
      }

      const result = await ecommerceService.importProducts(
        selectedPlatform,
        credentials
      );

      if (!result.success) {
        throw new Error(result.error || "Import failed");
      }

      setConnectionStatus("success");
      setImportStats({
        total: result.total,
        imported: result.imported,
        failed: (result.failedProducts || []).length,
      });

      setSuccess(true);
      setTimeout(() => {
        onImportComplete(result, {
          shopName: credentials.shopName,
          apiKey: credentials.apiKey,
          password: credentials.password,
        });
        onClose();
      }, 1500);
    } catch (err) {
      setConnectionStatus("error");
      setError(err instanceof Error ? err.message : "Import failed");
    } finally {
      setIsConnecting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-dark-400/80 backdrop-blur-sm flex items-center justify-center z-50 animate-fadeIn">
      <div
        className={`bg-dark-300 rounded-xl w-full max-w-2xl p-6 space-y-6 transform transition-all duration-300 ${
          success ? "scale-105 border-2 border-green-500/30" : ""
        }`}
      >
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            {platform && (
              <div className="w-8 h-8 rounded-lg bg-brand-500/10 flex items-center justify-center">
                <platform.icon
                  className="w-5 h-5 text-brand-300"
                  strokeWidth={1.5}
                />
              </div>
            )}
            <h2 className="text-xl font-light text-gray-100">
              Connect {platform?.name}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-dark-200 text-gray-400 transition-colors"
          >
            <X size={20} strokeWidth={1.5} />
          </button>
        </div>

        {/* Progress Steps */}
        {!success && (
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-2">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  step === "credentials"
                    ? "bg-brand-500 text-white"
                    : "bg-dark-200 text-gray-400"
                }`}
              >
                1
              </div>
              <div
                className={`w-24 h-0.5 ${
                  step === "credentials" ? "bg-dark-200" : "bg-brand-500"
                }`}
              />
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  step === "verify"
                    ? "bg-brand-500 text-white"
                    : "bg-dark-200 text-gray-400"
                }`}
              >
                2
              </div>
            </div>
          </div>
        )}

        {success ? (
          <div className="py-12 flex flex-col items-center justify-center space-y-4">
            <div className="w-16 h-16 rounded-full bg-green-500/10 flex items-center justify-center animate-scaleIn">
              <CheckCircle2
                className="w-8 h-8 text-green-400"
                strokeWidth={1.5}
              />
            </div>
            <h3 className="text-xl font-light text-gray-100">
              Connection Successful!
            </h3>
            {importStats && (
              <div className="text-sm text-gray-400 space-y-1">
                <p>Total products found: {importStats.total}</p>
                <p>Successfully imported: {importStats.imported}</p>
                {importStats.failed > 0 && (
                  <p className="text-yellow-400">
                    Failed to import: {importStats.failed}
                  </p>
                )}
              </div>
            )}
          </div>
        ) : (
          <div className="max-h-[calc(100vh-200px)] overflow-y-auto px-2">
            {step === "credentials" ? (
              <>
                <div className="flex items-start gap-3 p-4 rounded-lg bg-brand-500/5 border border-brand-500/10">
                  <div className="p-2 rounded-lg bg-brand-500/10">
                    <Key className="w-5 h-5 text-brand-300" strokeWidth={1.5} />
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm text-gray-300">
                      Authentication Required
                    </p>
                    <p className="text-sm text-gray-400">
                      Please provide your {platform?.name} credentials to
                      connect your store.
                    </p>
                  </div>
                </div>

                <div className="space-y-4 mt-6">
                  {platform?.fields?.map((field) => (
                    <div key={field.id} className="space-y-2">
                      <label className="block text-sm font-light text-gray-400 mb-2">
                        {field.label}
                        {field.required && (
                          <span className="text-red-400 ml-1">*</span>
                        )}
                      </label>
                      <div className="relative">
                        <input
                          type={
                            field.type === "password" &&
                            !visibleFields[field.id]
                              ? "password"
                              : "text"
                          }
                          value={credentials[field.id] || ""}
                          onChange={(e) =>
                            handleInputChange(field.id, e.target.value)
                          }
                          placeholder={field.placeholder}
                          className={`input pr-24 ${
                            validationErrors[field.id] ? "border-red-500" : ""
                          }`}
                        />
                        {field.type === "password" && (
                          <button
                            type="button"
                            onClick={() => toggleFieldVisibility(field.id)}
                            className="absolute right-2 top-1/2 -translate-y-1/2 p-2 text-gray-400 hover:text-gray-300"
                          >
                            {visibleFields[field.id] ? (
                              <EyeOff size={16} strokeWidth={1.5} />
                            ) : (
                              <Eye size={16} strokeWidth={1.5} />
                            )}
                          </button>
                        )}
                      </div>
                      {validationErrors[field.id] && (
                        <p className="text-sm text-red-400 mt-1 flex items-center gap-1">
                          <AlertCircle size={14} strokeWidth={1.5} />
                          {validationErrors[field.id]}
                        </p>
                      )}
                      {field.help && (
                        <div className="flex items-start gap-2 mt-2">
                          <HelpCircle
                            size={14}
                            className="text-gray-500 mt-0.5 flex-shrink-0"
                            strokeWidth={1.5}
                          />
                          <div className="text-xs text-gray-500">
                            <p>{field.help}</p>
                            {field.helpUrl && (
                              <a
                                href={field.helpUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-brand-300 hover:text-brand-400 inline-flex items-center gap-1 mt-1"
                              >
                                Learn more
                                <ExternalLink size={12} strokeWidth={1.5} />
                              </a>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                <div className="flex justify-end gap-3 pt-6">
                  <button onClick={onClose} className="btn btn-secondary">
                    Cancel
                  </button>
                  <button
                    onClick={handleNext}
                    disabled={!platform}
                    className={`btn btn-primary flex items-center gap-2 ${
                      Object.keys(credentials).length === 0
                        ? "opacity-50 cursor-not-allowed"
                        : ""
                    }`}
                  >
                    <span>Next</span>
                    <ArrowRight className="w-4 h-4" />
                  </button>
                </div>
              </>
            ) : step === "verify" ? (
              <>
                <div className="space-y-4">
                  <h3 className="text-lg font-light text-gray-200">
                    Verify Connection Details
                  </h3>
                  <div className="card rounded-lg p-4 space-y-3">
                    {Object.entries(credentials).map(([key, value]) => (
                      <div
                        key={key}
                        className="flex justify-between items-center"
                      >
                        <span className="text-sm text-gray-400">
                          {platform?.fields.find((f) => f.id === key)?.label ||
                            key}
                        </span>
                        <span className="text-sm text-gray-300">
                          {key.toLowerCase().includes("token") ||
                          key.toLowerCase().includes("secret") ||
                          key.toLowerCase().includes("key") ||
                          key.toLowerCase().includes("password")
                            ? "••••••••"
                            : value}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="flex justify-end gap-3 pt-6">
                  <button
                    onClick={() => setStep("credentials")}
                    className="btn btn-secondary"
                  >
                    Back
                  </button>
                  <button
                    onClick={handleImport}
                    disabled={isSubmitting}
                    className={`btn btn-primary flex items-center gap-2 ${
                      isSubmitting ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin" />
                        <span>Connecting...</span>
                      </>
                    ) : (
                      <>
                        <Link2 className="w-4 h-4" />
                        <span>Connect Store</span>
                      </>
                    )}
                  </button>
                </div>
              </>
            ) : null}
          </div>
        )}

        {error && (
          <div className="mt-4 p-4 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 flex items-center gap-2">
            <AlertCircle size={18} strokeWidth={1.5} />
            <span>{error}</span>
          </div>
        )}
      </div>

      {/* Shopify Product Selection Modal */}
      {showShopifyProductSelection && (
        <ShopifyProductSelectionModal
          isOpen={showShopifyProductSelection}
          onClose={() => setShowShopifyProductSelection(false)}
          credentials={{
            shopName: credentials.shopName,
            apiKey: credentials.apiKey,
            password: credentials.password,
          }}
          onImport={handleShopifyProductImport}
        />
      )}
    </div>
  );
};

export default ProductImportModal;
