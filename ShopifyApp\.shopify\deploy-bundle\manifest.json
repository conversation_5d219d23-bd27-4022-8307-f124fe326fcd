{"name": "modularcxtesting-3dviewer", "handle": "", "modules": [{"type": "theme_app_extension", "handle": "morpho-3d-viewer", "uid": "1076c969-768d-69ec-6871-7819bca2f65134024e82", "assets": "1076c969-768d-69ec-6871-7819bca2f65134024e82", "target": "", "config": {"theme_extension": {"files": {}}}}, {"type": "app_access", "handle": "app_access", "uid": "app_access", "assets": "app_access", "target": "", "config": {"scopes": "read_products,read_themes,write_themes", "redirect_url_allowlist": ["https://morpho.modularcx.io/auth/callback"]}}, {"type": "webhooks", "handle": "webhooks", "uid": "webhooks", "assets": "webhooks", "target": "", "config": {"api_version": "2025-01"}}, {"type": "point_of_sale", "handle": "point_of_sale", "uid": "point_of_sale", "assets": "point_of_sale", "target": "", "config": {"embedded": false}}, {"type": "app_home", "handle": "app_home", "uid": "app_home", "assets": "app_home", "target": "", "config": {"app_url": "https://morpho.modularcx.io", "embedded": true}}, {"type": "branding", "handle": "branding", "uid": "branding", "assets": "branding", "target": "", "config": {"name": "ModularCX-3d-viewer", "app_handle": "modularcx-3d"}}]}