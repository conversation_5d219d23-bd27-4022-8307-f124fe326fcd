# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

name = "3D Viewer App"
client_id = "d36f16d2a6e8767e33a54d83ba172a41"
application_url = "https://morpho.modularcx.io"
embedded = true
handle = "modularcx-3d"

[access_scopes]
scopes = "read_products,read_themes,write_themes"

[auth]
redirect_urls = [ "https://morpho.modularcx.io/auth/callback" ]

[webhooks]
api_version = "2025-01"

[[webhooks.endpoints]]
url = "https://api.modularcx.link/morpho/webhooks"
topics = [
  "customers/data_request",
  "customers/redact",
  "shop/redact",
  "app/uninstalled",
  "shop/update"
]

[pos]
embedded = false

[build]
dev_store_url = "modularcxtest.myshopify.com"
include_config_on_deploy = true
